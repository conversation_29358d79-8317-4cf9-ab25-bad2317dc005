# 🗺️ Location-Based Analytics Configuration Guide

This guide explains how to configure the location-based Google Ads analytics for different clients with different store locations.

## 📋 Overview

The location-based analytics system extracts location names from Google Ads campaign names and displays performance metrics for each location in beautiful, responsive cards.

## 🔧 Configuration

### Current Client: Quick Fix
- **Locations**: Daphne, Mobile, Foley
- **Campaign Patterns**: 
  - `"RSA Workaround Repair | Daphne #15"`
  - `"RL Buybacks | Mobile"`
  - `"Pmax | Foley"`

### Configuration File: `client-config.js`

The location configuration is stored in `window.CLIENT_CONFIG.googleAdsLocations`:

```javascript
googleAdsLocations: {
    // List of location names to extract from campaign names
    names: ['Daphne', 'Mobile', 'Foley'],
    
    // Custom icons for each location (emoji or FontAwesome)
    icons: {
        'Daphne': '🏪',
        'Mobile': '📱', 
        'Foley': '🏢'
    },
    
    // Custom colors for location cards
    colors: {
        'Daphne': '#4CAF50',
        'Mobile': '#2196F3',
        'Foley': '#FF9800'
    },
    
    // Location aliases (alternative names)
    aliases: {
        'Daphne': ['Daphne Store', 'Daphne Location'],
        'Mobile': ['Mobile Store', 'Mobile Location'],
        '<PERSON>': ['Foley Store', 'Foley Location']
    },
    
    // Matching options
    caseSensitive: false,
    partialMatch: true,
    
    // Defaults
    defaultIcon: '📍',
    defaultColor: '#6c757d'
}
```

## 🏢 Setting Up for Different Clients

### Example 1: Restaurant Chain with 4 Locations

```javascript
// In client-config.js, update the googleAdsLocations:
googleAdsLocations: {
    names: ['Downtown', 'Uptown', 'Westside', 'Eastside'],
    icons: {
        'Downtown': '🏙️',
        'Uptown': '🏘️', 
        'Westside': '🌅',
        'Eastside': '🌄'
    },
    colors: {
        'Downtown': '#FF5722',
        'Uptown': '#9C27B0',
        'Westside': '#FF9800',
        'Eastside': '#4CAF50'
    },
    aliases: {
        'Downtown': ['Downtown Store', 'City Center'],
        'Uptown': ['Uptown Location', 'North Store']
    }
}
```

**Campaign Examples**:
- `"Brand Awareness | Downtown Restaurant"`
- `"Local Deals | Uptown"`
- `"Weekend Special | Westside Location"`

### Example 2: Auto Repair with 3 Locations

```javascript
googleAdsLocations: {
    names: ['North Shop', 'South Shop', 'Central Shop'],
    icons: {
        'North Shop': '🔧',
        'South Shop': '🛠️',
        'Central Shop': '⚙️'
    },
    colors: {
        'North Shop': '#2196F3',
        'South Shop': '#4CAF50', 
        'Central Shop': '#FF9800'
    }
}
```

### Example 3: Medical Practice with 2 Locations

```javascript
googleAdsLocations: {
    names: ['Main Clinic', 'Satellite Office'],
    icons: {
        'Main Clinic': '🏥',
        'Satellite Office': '🩺'
    },
    colors: {
        'Main Clinic': '#2196F3',
        'Satellite Office': '#4CAF50'
    }
}
```

## 🚀 Quick Setup Methods

### Method 1: Using Helper Functions

```javascript
// For a new client, use the helper function:
CLIENT_CONFIG.updateGoogleAdsLocations(
    ['Store A', 'Store B', 'Store C'], 
    {
        icons: { 'Store A': '🏪', 'Store B': '🏬', 'Store C': '🏢' },
        colors: { 'Store A': '#FF5722', 'Store B': '#4CAF50', 'Store C': '#2196F3' }
    }
);
```

### Method 2: Direct Configuration

Simply edit the `googleAdsLocations` object in `client-config.js` with the new client's location names and preferences.

## 🧪 Testing Your Configuration

After updating the configuration, test it using the browser console:

```javascript
// Test location extraction
testLocationAnalytics()

// Test with sample data
testLocationCards()

// Manually process current data
processLocations()

// Get current metrics
getLocationMetrics()
```

## 📊 Features

### Automatic Features
- ✅ **Responsive Design**: 4 cards on desktop, 2 on tablet, 1 on mobile
- ✅ **Date Filtering**: Updates automatically with date filter changes
- ✅ **Error Handling**: Loading states, error messages, retry functionality
- ✅ **Performance**: Optimized for large datasets
- ✅ **Validation**: Cross-verification of calculations

### Metrics Displayed
- 💰 **Total Spend**
- 👆 **Total Clicks** 
- 💲 **Average CPC**
- 🎯 **Total Conversions**
- 📈 **Cost Per Lead (CPL)**
- 📊 **Click-Through Rate (CTR)**

## 🔄 Migration Steps

1. **Backup**: Save current `client-config.js`
2. **Update**: Modify `googleAdsLocations` section
3. **Test**: Use console testing functions
4. **Verify**: Check that location cards appear correctly
5. **Deploy**: Push changes to production

## 💡 Tips

- **Location Names**: Use the exact names as they appear in your Google Ads campaigns
- **Icons**: Use emoji or FontAwesome classes for best results
- **Colors**: Use hex codes for consistent branding
- **Aliases**: Add alternative names if campaigns use variations
- **Testing**: Always test with real campaign data before deploying

## 🆘 Troubleshooting

- **No cards showing**: Check that location names match campaign names exactly
- **Wrong locations**: Verify `names` array in configuration
- **Missing icons**: Check `icons` object has entries for all locations
- **Performance issues**: Use browser dev tools to check for JavaScript errors

The system is designed to be **completely scalable** and can handle any number of locations with any naming convention!
