#!/usr/bin/env python3
"""
Test script for the export functionality
"""

import sqlite3
import os
from datetime import datetime

def setup_test_data():
    """Create test database with sample clients"""
    db_path = "clients.db"
    
    # Remove existing database
    if os.path.exists(db_path):
        os.remove(db_path)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create clients table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS clients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            store_name TEXT NOT NULL UNIQUE,
            customer_id TEXT NOT NULL,
            business_name TEXT,
            notes TEXT,
            created_date TEXT,
            last_updated TEXT
        )
    ''')
    
    # Insert test data
    test_clients = [
        ("Fix My Gadget", "1234567890", "Fix My Gadget Electronics", "Main repair shop", "2024-01-15 10:30:00", "2024-01-15 10:30:00"),
        ("Mobile Masters", "9876543210", "Mobile Masters Inc", "Phone repair specialist", "2024-02-20 14:15:00", "2024-02-20 14:15:00"),
        ("Tech Solutions", "5555666777", "Tech Solutions LLC", "Computer and electronics", "2024-03-10 09:45:00", "2024-03-10 09:45:00"),
        ("Quick Fix", "1111222333", "Quick Fix Store", "Fast repair service", "2024-04-05 16:20:00", "2024-04-05 16:20:00"),
        ("Gadget Guru", "4444555666", "Gadget Guru Repairs", "", "2024-05-12 11:10:00", "2024-05-12 11:10:00")
    ]
    
    for client in test_clients:
        cursor.execute('''
            INSERT INTO clients (store_name, customer_id, business_name, notes, created_date, last_updated)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', client)
    
    conn.commit()
    conn.close()
    
    print(f"✅ Created test database with {len(test_clients)} clients")
    print("Test clients:")
    for i, client in enumerate(test_clients, 1):
        print(f"  {i}. {client[0]} (ID: {client[1]})")

def verify_database():
    """Verify the database was created correctly"""
    db_path = "clients.db"
    
    if not os.path.exists(db_path):
        print("❌ Database not found!")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute('SELECT COUNT(*) FROM clients')
    count = cursor.fetchone()[0]
    
    cursor.execute('SELECT * FROM clients ORDER BY store_name')
    clients = cursor.fetchall()
    
    conn.close()
    
    print(f"✅ Database verified: {count} clients found")
    return True

if __name__ == "__main__":
    print("🧪 Setting up test data for export functionality...")
    setup_test_data()
    verify_database()
    print("\n🚀 Now you can run the main app and test the export feature!")
    print("   1. Run: python 'RLDB Customizer 2.py'")
    print("   2. Go to Client Configuration tab")
    print("   3. Click 'Manage Clients'")
    print("   4. Click 'Export' button")
    print("   5. Test different export formats")
