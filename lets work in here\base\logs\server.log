2025-07-15 12:35:08 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 12:35:08 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test14\quick_fix_dashboard
2025-07-15 12:35:08 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 12:35:08 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 12:35:08 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 12:35:08 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 12:35:08 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 12:35:17 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:35:17] "GET / HTTP/1.1" 200 -
2025-07-15 12:35:17 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:35:17] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 12:35:17 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:35:17] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 12:35:17 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:35:17] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 12:35:17 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:35:17] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-15 12:35:17 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:35:17] "GET /script.js HTTP/1.1" 200 -
2025-07-15 12:35:22 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:35:22] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 12:35:34 | INFO     | root | Server-side pagination complete: 2397 total records from 24 pages
2025-07-15 12:35:34 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:35:34] "GET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf HTTP/1.1" 200 -
2025-07-15 12:35:35 | INFO     | root | Server-side pagination complete: 2483 total records from 25 pages
2025-07-15 12:35:35 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 12:35:35] "GET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn HTTP/1.1" 200 -
2025-07-15 14:01:56 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 14:01:56 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test14\quick_fix_dashboard
2025-07-15 14:01:56 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 14:01:56 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 14:01:56 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 14:01:56 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 14:01:56 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 14:01:59 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 14:01:59] "GET / HTTP/1.1" 200 -
2025-07-15 14:01:59 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 14:01:59] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 14:01:59 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 14:01:59] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 14:01:59 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 14:01:59] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 14:01:59 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 14:01:59] "GET /script.js HTTP/1.1" 200 -
2025-07-15 14:01:59 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 14:01:59] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-15 14:02:03 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 14:02:03] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 14:02:14 | INFO     | root | Server-side pagination complete: 2397 total records from 24 pages
2025-07-15 14:02:14 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 14:02:14] "GET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf HTTP/1.1" 200 -
2025-07-15 14:02:15 | INFO     | root | Server-side pagination complete: 2483 total records from 25 pages
2025-07-15 14:02:15 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 14:02:15] "GET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn HTTP/1.1" 200 -
2025-07-15 14:26:19 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 14:26:19 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test14\quick_fix_dashboard
2025-07-15 14:26:19 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 14:26:19 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 14:26:19 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 14:26:19 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 14:26:19 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 14:26:36 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 14:26:36] "GET / HTTP/1.1" 200 -
2025-07-15 14:26:36 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 14:26:36] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 14:26:36 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 14:26:36] "[36mGET /client-config.js HTTP/1.1[0m" 304 -
2025-07-15 14:26:36 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 14:26:36] "[36mGET /img/rl.svg HTTP/1.1[0m" 304 -
2025-07-15 14:26:36 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 14:26:36] "GET /script.js HTTP/1.1" 200 -
2025-07-15 14:26:40 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 14:26:40] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-15 14:27:00 | INFO     | root | Server-side pagination complete: 2397 total records from 24 pages
2025-07-15 14:27:00 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 14:27:00] "GET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf HTTP/1.1" 200 -
2025-07-15 14:27:00 | INFO     | root | Server-side pagination complete: 2483 total records from 25 pages
2025-07-15 14:27:00 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 14:27:00] "GET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn HTTP/1.1" 200 -
2025-07-15 14:29:30 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 14:29:30] "HEAD / HTTP/1.1" 200 -
2025-07-15 21:54:25 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 21:54:25 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\Reapir Lift Attribution Dashboards\--RLDB Base--\test base
2025-07-15 21:54:25 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 21:54:25 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 21:54:25 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 21:54:25 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 21:54:25 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 21:54:31 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 21:54:31] "GET / HTTP/1.1" 200 -
2025-07-15 21:54:31 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 21:54:31] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 21:54:31 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 21:54:31] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 21:54:31 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 21:54:31] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 21:54:31 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 21:54:31] "GET /script.js HTTP/1.1" 200 -
2025-07-15 21:57:56 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-15 21:57:56 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\Reapir Lift Attribution Dashboards\--RLDB Base--\test base
2025-07-15 21:57:56 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-15 21:57:56 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-15 21:57:56 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-15 21:57:56 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-15 21:57:56 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-15 21:58:01 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 21:58:01] "GET / HTTP/1.1" 200 -
2025-07-15 21:58:01 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 21:58:01] "GET /client-config.js HTTP/1.1" 200 -
2025-07-15 21:58:01 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 21:58:01] "GET /styles.css HTTP/1.1" 200 -
2025-07-15 21:58:01 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 21:58:01] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-15 21:58:01 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 21:58:01] "GET /script.js HTTP/1.1" 200 -
2025-07-15 21:58:14 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 21:58:14] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-15 21:58:14 | INFO     | werkzeug | 127.0.0.1 - - [15/Jul/2025 21:58:14] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-16 12:21:22 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-16 12:21:22 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\test14\quick_fix_dashboard\lets work in here\base
2025-07-16 12:21:22 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-16 12:21:22 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-16 12:21:22 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-16 12:21:22 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-16 12:21:22 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-16 12:21:32 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:21:32] "GET / HTTP/1.1" 200 -
2025-07-16 12:21:32 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:21:32] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-16 12:21:32 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:21:32] "GET /styles.css HTTP/1.1" 200 -
2025-07-16 12:21:32 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:21:32] "GET /client-config.js HTTP/1.1" 200 -
2025-07-16 12:21:32 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:21:32] "GET /script.js HTTP/1.1" 200 -
2025-07-16 12:22:02 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:22:02] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-16 12:22:02 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:22:02] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-16 12:25:05 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:25:05] "[36mGET / HTTP/1.1[0m" 304 -
2025-07-16 12:25:05 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:25:05] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-16 12:25:05 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:25:05] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-16 12:25:05 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:25:05] "[36mGET /img/rl.svg HTTP/1.1[0m" 304 -
2025-07-16 12:25:05 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:25:05] "GET /client-config.js HTTP/1.1" 200 -
2025-07-16 12:25:05 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:25:05] "[36mGET /script.js HTTP/1.1[0m" 304 -
2025-07-16 12:25:09 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:25:09] "[33mGET /api/latest-data-date HTTP/1.1[0m" 404 -
2025-07-16 12:25:30 | INFO     | root | Server-side pagination complete: 2397 total records from 24 pages
2025-07-16 12:25:30 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:25:30] "GET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf HTTP/1.1" 200 -
2025-07-16 12:25:30 | INFO     | root | Server-side pagination complete: 2483 total records from 25 pages
2025-07-16 12:25:30 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:25:30] "GET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn HTTP/1.1" 200 -
2025-07-16 12:33:39 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:33:39] "[36mGET / HTTP/1.1[0m" 304 -
2025-07-16 12:33:39 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:33:39] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-16 12:33:39 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:33:39] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-16 12:33:39 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:33:39] "[36mGET /client-config.js HTTP/1.1[0m" 304 -
2025-07-16 12:33:39 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:33:39] "[36mGET /img/rl.svg HTTP/1.1[0m" 304 -
2025-07-16 12:33:39 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:33:39] "GET /script.js HTTP/1.1" 200 -
2025-07-16 12:33:55 | INFO     | root | Server-side pagination complete: 2397 total records from 24 pages
2025-07-16 12:33:55 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:33:55] "GET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf HTTP/1.1" 200 -
2025-07-16 12:33:56 | INFO     | root | Server-side pagination complete: 2483 total records from 25 pages
2025-07-16 12:33:56 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:33:56] "GET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn HTTP/1.1" 200 -
2025-07-16 12:59:56 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:59:56] "[36mGET / HTTP/1.1[0m" 304 -
2025-07-16 12:59:56 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:59:56] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-16 12:59:56 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:59:56] "[36mGET /client-config.js HTTP/1.1[0m" 304 -
2025-07-16 12:59:56 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:59:56] "[36mGET /img/rl.svg HTTP/1.1[0m" 304 -
2025-07-16 12:59:56 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:59:56] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-07-16 12:59:56 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 12:59:56] "GET /script.js HTTP/1.1" 200 -
2025-07-16 13:00:18 | INFO     | root | Server-side pagination complete: 2397 total records from 24 pages
2025-07-16 13:00:18 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 13:00:18] "GET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf HTTP/1.1" 200 -
2025-07-16 13:00:19 | INFO     | root | Server-side pagination complete: 2483 total records from 25 pages
2025-07-16 13:00:19 | INFO     | werkzeug | 127.0.0.1 - - [16/Jul/2025 13:00:19] "GET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn HTTP/1.1" 200 -
